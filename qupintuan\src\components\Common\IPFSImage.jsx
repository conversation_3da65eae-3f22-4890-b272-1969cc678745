// src/components/Common/IPFSImage.jsx
import { useState } from 'react';
import './IPFSImage.css';

/**
 * IPFS图片显示组件
 * 支持多网关自动切换，确保图片能够正常显示
 */
export default function IPFSImage({ 
  hash, 
  alt = '图片', 
  className = '', 
  style = {},
  onLoad,
  onError,
  ...props 
}) {
  const [currentGatewayIndex, setCurrentGatewayIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // IPFS网关列表 (按速度和可靠性排序)
  const ipfsGateways = [
    'https://gateway.pinata.cloud/ipfs',
    'https://ipfs.io/ipfs',
    'https://cloudflare-ipfs.com/ipfs',
    'https://dweb.link/ipfs',
    'https://ipfs.infura.io/ipfs'
  ];

  // 构建当前图片URL
  const currentImageUrl = `${ipfsGateways[currentGatewayIndex]}/${hash}`;

  // 处理图片加载成功
  const handleLoad = (e) => {
    setIsLoading(false);
    setHasError(false);

    if (onLoad) {
      onLoad(e);
    }
  };

  // 处理图片加载失败
  const handleError = (e) => {
    // 尝试下一个网关
    if (currentGatewayIndex < ipfsGateways.length - 1) {
      setCurrentGatewayIndex(prev => prev + 1);
    } else {
      // 所有网关都失败了
      setIsLoading(false);
      setHasError(true);

      if (onError) {
        onError(e);
      }
    }
  };

  // 重试加载
  const handleRetry = () => {
    setCurrentGatewayIndex(0);
    setIsLoading(true);
    setHasError(false);
  };

  // 如果没有哈希，显示占位符
  if (!hash) {
    return (
      <div className={`ipfs-image-placeholder ${className}`} style={style} {...props}>
        <div className="placeholder-content">
          <div className="placeholder-icon">📷</div>
          <div className="placeholder-text">暂无图片</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`ipfs-image-container ${className}`} style={style}>
      {/* 加载状态 */}
      {isLoading && (
        <div className="ipfs-image-loading">
          <div className="loading-spinner"></div>
          <div className="loading-text">加载中...</div>
        </div>
      )}

      {/* 错误状态 */}
      {hasError && (
        <div className="ipfs-image-error">
          <div className="error-icon">❌</div>
          <div className="error-text">图片加载失败</div>
          <button className="retry-btn" onClick={handleRetry}>
            🔄 重试
          </button>
        </div>
      )}

      {/* 实际图片 */}
      <img
        src={currentImageUrl}
        alt={alt}
        onLoad={handleLoad}
        onError={handleError}
        style={{
          display: (isLoading || hasError) ? 'none' : 'block',
          width: '100%',
          height: '100%',
          objectFit: 'cover'
        }}
        {...props}
      />

      {/* 网关信息 (开发模式显示) */}
      {process.env.NODE_ENV === 'development' && !isLoading && !hasError && (
        <div className="gateway-info">
          网关 {currentGatewayIndex + 1}
        </div>
      )}
    </div>
  );
}

/**
 * IPFS图片网格组件
 * 用于显示多张IPFS图片
 */
export function IPFSImageGrid({ 
  hashes = [], 
  className = '', 
  maxImages = 5,
  onImageClick,
  ...props 
}) {
  if (!hashes || hashes.length === 0) {
    return (
      <div className={`ipfs-image-grid empty ${className}`} {...props}>
        <div className="empty-grid">
          <div className="empty-icon">📷</div>
          <div className="empty-text">暂无图片</div>
        </div>
      </div>
    );
  }

  const displayHashes = hashes.slice(0, maxImages);

  return (
    <div className={`ipfs-image-grid ${className}`} {...props}>
      {displayHashes.map((hash, index) => (
        <div 
          key={hash || index} 
          className="grid-item"
          onClick={() => onImageClick && onImageClick(hash, index)}
        >
          <IPFSImage
            hash={hash}
            alt={`图片 ${index + 1}`}
            className="grid-image"
          />
          {index === 0 && displayHashes.length > 1 && (
            <div className="main-badge">主图</div>
          )}
        </div>
      ))}
      
      {hashes.length > maxImages && (
        <div className="more-indicator">
          +{hashes.length - maxImages}
        </div>
      )}
    </div>
  );
}

/**
 * IPFS图片预览组件
 * 用于商品详情页的大图预览
 */
export function IPFSImagePreview({ 
  hashes = [], 
  currentIndex = 0, 
  onIndexChange,
  className = '',
  ...props 
}) {
  if (!hashes || hashes.length === 0) {
    return (
      <div className={`ipfs-image-preview empty ${className}`} {...props}>
        <div className="preview-placeholder">
          <div className="placeholder-icon">📷</div>
          <div className="placeholder-text">暂无图片</div>
        </div>
      </div>
    );
  }

  const currentHash = hashes[currentIndex];

  return (
    <div className={`ipfs-image-preview ${className}`} {...props}>
      {/* 主图显示 */}
      <div className="preview-main">
        <IPFSImage
          hash={currentHash}
          alt={`商品图片 ${currentIndex + 1}`}
          className="main-image"
        />
        
        {/* 图片导航 */}
        {hashes.length > 1 && (
          <>
            <button 
              className="nav-btn prev"
              onClick={() => onIndexChange && onIndexChange(
                currentIndex > 0 ? currentIndex - 1 : hashes.length - 1
              )}
              disabled={hashes.length <= 1}
            >
              ←
            </button>
            <button 
              className="nav-btn next"
              onClick={() => onIndexChange && onIndexChange(
                currentIndex < hashes.length - 1 ? currentIndex + 1 : 0
              )}
              disabled={hashes.length <= 1}
            >
              →
            </button>
          </>
        )}
      </div>

      {/* 缩略图列表 */}
      {hashes.length > 1 && (
        <div className="preview-thumbnails">
          {hashes.map((hash, index) => (
            <div
              key={hash || index}
              className={`thumbnail ${index === currentIndex ? 'active' : ''}`}
              onClick={() => onIndexChange && onIndexChange(index)}
            >
              <IPFSImage
                hash={hash}
                alt={`缩略图 ${index + 1}`}
                className="thumbnail-image"
              />
            </div>
          ))}
        </div>
      )}

      {/* 图片计数 */}
      <div className="image-counter">
        {currentIndex + 1} / {hashes.length}
      </div>
    </div>
  );
}
