// src/components/Mall/ProductPurchase.jsx
import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { toast } from 'react-hot-toast';
import { buyProduct, buyProductWithDefaultAddress } from '@/apis/mallApi';
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts';
import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';
import { formatPoints, parsePointsInput } from '@/utils/pointsFormatter';
import { IPFSImageGrid } from '@/components/Common/IPFSImage';
import AddressSelector from './AddressSelector';
// import { getUserPoints } from '@/apis/pointsApi';
import './ProductPurchase.css';

export default function ProductPurchase({ product, onPurchaseSuccess, onClose }) {
  const { address: account } = useAccount();

  // 状态管理
  const [quantity, setQuantity] = useState(1);
  const [userPoints, setUserPoints] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);
  const [userAddresses, setUserAddresses] = useState([]);
  const [selectedAddressId, setSelectedAddressId] = useState(null);
  const [currentDefaultAddress, setCurrentDefaultAddress] = useState(null);
  const [showAddressSelector, setShowAddressSelector] = useState(false);
  const [isLoadingAddresses, setIsLoadingAddresses] = useState(false);
  const [showImageGallery, setShowImageGallery] = useState(false);

  // 获取数值价格用于计算 - 使用正确的格式化函数
  const fixedPrice = parseFloat(formatPoints(product.price)) || 0;



  // 计算总价
  const totalPrice = fixedPrice * quantity;
  const canAfford = userPoints >= totalPrice;
  const maxQuantity = Math.min(product.stock, Math.floor(userPoints / fixedPrice));



  // 加载用户积分
  const loadUserPoints = async () => {
    if (!account) return;


    setIsLoading(true);
    try {
      // 查询真实的用户积分余额
      const { CONTRACT_ADDRESSES, ABIS } = await import('@/contracts');
      const { createPublicClient, http } = await import('viem');
      const { bscTestnet } = await import('viem/chains');

      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });

      const pointsAddress = CONTRACT_ADDRESSES[97].PointsManagement;

      // 查询用户的拼团积分（用于购买商品）
      const groupBuyPoints = await publicClient.readContract({
        address: pointsAddress,
        abi: ABIS.PointsManagement,
        functionName: 'groupBuyPointsNonExchangeable',
        args: [account]
      });

      // 积分系统使用6位精度
      const { formatUnits } = await import('viem');
      const pointsBalance = parseFloat(formatUnits(groupBuyPoints, 6));
      setUserPoints(pointsBalance);
    } catch (error) {
      toast.error(`加载积分失败: ${error.message}`);
      setUserPoints(0); // 出错时设置为0
    } finally {
      setIsLoading(false);
    }
  };

  // 加载用户收货地址
  const loadUserAddresses = async () => {
    if (!account) {
      setUserAddresses([]);
      setCurrentDefaultAddress(null);
      setSelectedAddressId(null);
      return;
    }


    setIsLoadingAddresses(true);

    let addressesLoaded = false;

    try {
      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });

      const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement;

      // 尝试使用新的 getMyAddresses 函数
      try {
        const addresses = await publicClient.readContract({
          address: addressAddress,
          abi: ABIS.AddressManagement,
          functionName: 'getMyAddresses',
          args: [],
          account
        });
        setUserAddresses(addresses || []);

        // 如果有地址，自动选择默认地址或第一个地址
        if (addresses && addresses.length > 0) {
          const defaultAddress = addresses.find(addr => addr.isDefault);
          const selectedAddress = defaultAddress || addresses[0]; // 选择默认地址或第一个地址
          const rawAddressId = selectedAddress.addressId;

          // 尝试不同的数据类型转换
          let selectedId;
          if (typeof rawAddressId === 'bigint') {
            selectedId = Number(rawAddressId); // 先尝试 Number
          } else {
            selectedId = rawAddressId;
          }
          setSelectedAddressId(selectedId);

          // 设置当前默认地址状态
          setCurrentDefaultAddress(selectedAddress);
        }
        addressesLoaded = true;
      } catch (newFunctionError) {
        // 如果新函数不存在，回退到旧函数
        try {
          const addresses = await publicClient.readContract({
            address: addressAddress,
            abi: ABIS.AddressManagement,
            functionName: 'getUserAddresses',
            args: [account]
          });
          setUserAddresses(addresses || []);

          // 如果有地址，自动选择默认地址或第一个地址
          if (addresses && addresses.length > 0) {
            const defaultAddress = addresses.find(addr => addr.isDefault);
            const selectedAddress = defaultAddress || addresses[0]; // 选择默认地址或第一个地址
            const rawAddressId = selectedAddress.addressId;

            // 将 BigInt 转换为数字
            const selectedId = typeof rawAddressId === 'bigint' ? Number(rawAddressId) : rawAddressId;
            setSelectedAddressId(selectedId);

            // 设置当前默认地址状态
            setCurrentDefaultAddress(selectedAddress);
          }
          addressesLoaded = true;
        } catch (authError) {
          // 静默处理地址加载失败
        }
      }
    } catch (error) {
      // 静默处理地址加载错误
    } finally {
      // 只有在地址加载失败时才清空状态
      if (!addressesLoaded) {
        setUserAddresses([]);
        setSelectedAddressId(null);
        setCurrentDefaultAddress(null);
      }

      setIsLoadingAddresses(false);
    }
  };

  // 处理地址变化
  const handleAddressChange = (defaultAddress) => {
    setCurrentDefaultAddress(defaultAddress);

    if (defaultAddress) {
      const addressId = typeof defaultAddress.addressId === 'bigint' ? Number(defaultAddress.addressId) : defaultAddress.addressId;
      setSelectedAddressId(addressId);
    }
  };

  // 处理购买
  const handlePurchase = async () => {
    if (!account) {
      toast.error('请先连接钱包');
      return;
    }

    if (quantity <= 0 || quantity > product.stock) {
      toast.error('购买数量无效');
      return;
    }

    if (!canAfford) {
      toast.error('积分余额不足');
      return;
    }

    // 验证收货地址（使用默认地址）
    if (!currentDefaultAddress) {
      toast.error('请先设置默认收货地址才能购买商品', {
        duration: 6000,
      });

      // 显示详细的引导信息
      setTimeout(() => {
        toast((t) => (
          <div style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: '10px' }}>
              <strong>📍 需要设置收货地址</strong>
            </div>
            <div style={{ marginBottom: '10px', fontSize: '14px' }}>
              请前往 <strong>个人中心 → 收货地址</strong> 添加并设置默认地址
            </div>
            <button
              onClick={() => {
                toast.dismiss(t.id);
                // 可以在这里添加跳转到个人中心的逻辑
                window.location.href = '/profile';
              }}
              style={{
                background: '#007bff',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '4px',
                cursor: 'pointer',
                marginRight: '8px'
              }}
            >
              前往设置
            </button>
            <button
              onClick={() => toast.dismiss(t.id)}
              style={{
                background: '#6c757d',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              关闭
            </button>
          </div>
        ), {
          duration: 10000,
        });
      }, 1000);

      setShowAddressSelector(true);
      return;
    }



    setIsPurchasing(true);
    try {

      // 创建 signer
      const signer = {
        account: { address: account },
        address: account
      };

      // 直接调用新的默认地址购买函数
      const result = await buyProductWithDefaultAddress({
        productId: product.productId,
        quantity,
        signer
      });



      toast.success(`🎉 趣拼团购买成功！消耗 ${totalPrice.toFixed(2)} 积分 - 愛拼才会赢！`, {
        duration: 4000,
        position: 'top-center',
      });

      // 通知父组件购买成功
      if (onPurchaseSuccess) {
        onPurchaseSuccess(result);
      }

      // 关闭购买弹窗
      if (onClose) {
        onClose();
      }

    } catch (error) {

      let errorMessage = error.message;
      if (error.message.includes('Insufficient points')) {
        errorMessage = '积分余额不足';
      } else if (error.message.includes('Insufficient stock')) {
        errorMessage = '商品库存不足';
      } else if (error.message.includes('Product not active')) {
        errorMessage = '商品已下架';
      } else if (error.message.includes('请先注册代理系统')) {
        errorMessage = '请先注册代理系统才能购买商品';
      }

      toast.error(`购买失败: ${errorMessage}`);
    } finally {
      setIsPurchasing(false);
    }
  };

  // 组件挂载时加载积分
  useEffect(() => {
    loadUserPoints();
    loadUserAddresses();
  }, [account]); // eslint-disable-line react-hooks/exhaustive-deps

  // 组件挂载时确保 isPurchasing 状态为 false
  useEffect(() => {
    if (isPurchasing) {
      setIsPurchasing(false);
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div className="product-purchase">
      <div className="purchase-overlay" onClick={onClose}></div>

      <div className="purchase-modal">
        <div className="modal-header">
          <h3>🛒 购买商品</h3>
          <button className="close-btn" onClick={onClose}>✕</button>
        </div>

        <div className="modal-content">
          {/* 商品信息 */}
          <div className="product-summary">
            <div className="product-image" onClick={() => setShowImageGallery(true)}>
              {product.images && product.images.length > 0 ? (
                <>
                  <img
                    src={product.images[0].startsWith('http')
                      ? product.images[0]
                      : `https://ipfs.io/ipfs/${product.images[0]}`}
                    alt={product.name}
                    onError={(e) => {
                      e.target.style.display = 'none';
                    }}
                  />
                  {product.images.length > 1 && (
                    <div className="image-count-badge">
                      📷 {product.images.length}
                    </div>
                  )}
                </>
              ) : (
                <div className="placeholder-image">📦</div>
              )}
            </div>

            <div className="product-details">
              <h4 className="product-name">{product.name}</h4>
              <p className="product-description">
                {product.description.length > 100
                  ? `${product.description.substring(0, 100)}...`
                  : product.description
                }
              </p>
              <div className="product-meta">
                <span className="product-price">{formatPoints(product.price)} 积分/件</span>
                <span className="product-stock">库存: {product.stock} 件</span>
              </div>
            </div>
          </div>

          {/* 用户积分信息 */}
          <div className="user-points">
            <div className="points-info">
              <span className="points-label">我的积分:</span>
              <span className="points-value">
                {isLoading ? '加载中...' : `${userPoints} 积分`}
              </span>
            </div>
            {!canAfford && (
              <div className="insufficient-notice">
                ⚠️ 积分余额不足，无法购买
              </div>
            )}
          </div>

          {/* 收货地址显示 */}
          <div className="address-section">
            <div className="address-label">收货地址:</div>
            {isLoadingAddresses ? (
              <div className="address-loading">加载地址中...</div>
            ) : userAddresses.length === 0 ? (
              <div className="no-address">
                <div className="no-address-notice">
                  ⚠️ 您还没有添加收货地址
                </div>
                <div className="no-address-hint">
                  请先到个人中心添加收货地址才能购买商品
                </div>
              </div>
            ) : currentDefaultAddress ? (
              <div className="default-address-display">
                <div className="address-card">
                  <div className="address-header">
                    <span className="address-name">{currentDefaultAddress.name}</span>
                    <span className="address-phone">{currentDefaultAddress.phone}</span>
                    <span className="default-tag">默认地址</span>
                  </div>
                  <div className="address-detail">
                    {currentDefaultAddress.province} {currentDefaultAddress.city} {currentDefaultAddress.district} {currentDefaultAddress.detail}
                  </div>
                </div>
                <div className="address-change-hint">
                  💡 如需更换收货地址，请到个人中心设置其他地址为默认地址
                </div>
              </div>
            ) : (
              <div className="no-default-address">
                <div className="no-address-notice">
                  ⚠️ 未找到默认收货地址
                </div>
                <div className="no-address-hint">
                  请到个人中心设置一个默认收货地址
                </div>
              </div>
            )}
          </div>

          {/* 购买数量选择 */}
          <div className="quantity-section">
            <div className="quantity-label">购买数量:</div>
            <div className="quantity-controls">
              <button
                className="quantity-btn"
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                disabled={quantity <= 1}
              >
                -
              </button>
              <input
                type="number"
                value={quantity}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 1;
                  setQuantity(Math.max(1, Math.min(maxQuantity, value)));
                }}
                min="1"
                max={maxQuantity}
                className="quantity-input"
              />
              <button
                className="quantity-btn"
                onClick={() => setQuantity(Math.min(maxQuantity, quantity + 1))}
                disabled={quantity >= maxQuantity}
              >
                +
              </button>
            </div>
            <div className="quantity-hint">
              最多可购买 {maxQuantity} 件
            </div>
          </div>

          {/* 价格计算 */}
          <div className="price-calculation">
            <div className="calc-row">
              <span>单价:</span>
              <span>{formatPoints(product.price)} 积分</span>
            </div>
            <div className="calc-row">
              <span>数量:</span>
              <span>{quantity} 件</span>
            </div>
            <div className="calc-row total">
              <span>总计:</span>
              <span className="total-price">{totalPrice.toFixed(2)} 积分</span>
            </div>
          </div>
        </div>

        {/* 地址选择器 */}
        {showAddressSelector && (
          <div className="address-selector-section">
            <AddressSelector
              onAddressChange={handleAddressChange}
              className="purchase-address-selector"
            />
          </div>
        )}



        <div className="modal-actions">
          <button
            className="cancel-btn"
            onClick={onClose}
            disabled={isPurchasing}
          >
            取消
          </button>
          <button
            className="purchase-btn"
            onClick={handlePurchase}
            disabled={isPurchasing || !canAfford || quantity <= 0 || quantity > maxQuantity || !currentDefaultAddress}
          >
            {isPurchasing ? '⏳ 购买中...' :
             !currentDefaultAddress ? '❌ 请先设置默认地址' :
             `💰 确认购买 (${totalPrice.toFixed(2)} 积分)`}
          </button>

          {!currentDefaultAddress && (
            <button
              className="address-setup-btn"
              onClick={() => {
                window.location.href = '/profile';
              }}
              style={{
                width: '100%',
                marginTop: '10px',
                background: '#28a745',
                color: 'white',
                border: 'none',
                padding: '12px',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '600'
              }}
            >
              📍 前往个人中心设置收货地址
            </button>
          )}
        </div>

        {/* 购买说明 */}
        <div className="purchase-notice">
          <h5>📋 购买说明</h5>
          <div className="notice-items">
            <div className="notice-item">• 购买成功后积分立即扣除，无法撤销</div>
            <div className="notice-item">• 系统将自动使用您的默认收货地址发货</div>
            <div className="notice-item">• 如需更换收货地址，请先在个人中心设置新的默认地址</div>
            {!currentDefaultAddress && (
              <div className="notice-item" style={{ color: '#dc3545', fontWeight: 'bold' }}>
                ⚠️ 您还没有设置默认收货地址，无法完成购买
              </div>
            )}
          </div>
        </div>

        {/* 图片画廊 */}
        {showImageGallery && product.images && product.images.length > 0 && (
          <div className="image-gallery-overlay" onClick={() => setShowImageGallery(false)}>
            <div className="image-gallery-modal" onClick={(e) => e.stopPropagation()}>
              <div className="gallery-header">
                <h4>商品图片 ({product.images.length})</h4>
                <button
                  className="gallery-close-btn"
                  onClick={() => setShowImageGallery(false)}
                >
                  ✕
                </button>
              </div>
              <div className="gallery-content">
                <IPFSImageGrid
                  hashes={product.images}
                  maxImages={product.images.length}
                  className="purchase-image-grid"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
