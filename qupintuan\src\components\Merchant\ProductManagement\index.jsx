// src/components/Merchant/ProductManagement/index.jsx
import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { toast } from 'react-hot-toast';
import { getMerchantProducts, createProduct, updateProduct, toggleProductStatus, deleteProduct } from '@/apis/mallApi';
import { formatPoints } from '@/utils/pointsFormatter';
import { IPFSImageGrid } from '@/components/Common/IPFSImage';
import AddProduct from './AddProduct';
import OrderManagement from '../OrderManagement';
import './index.css';

export default function ProductManagement() {
  const { address: account } = useAccount();
  const [activeTab, setActiveTab] = useState('list');
  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showImageGallery, setShowImageGallery] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [totalProducts, setTotalProducts] = useState(0);

  // 加载商品列表
  const loadProducts = async (page = currentPage) => {
    if (!account) return;

    setIsLoading(true);
    try {
      // 获取商家的商品ID列表
      const productIds = await getMerchantProducts({ merchantAddress: account });

      if (productIds.length === 0) {
        setProducts([]);
        setTotalProducts(0);
        return;
      }

      // 获取所有商品的详细信息以过滤已删除的商品
      const { getProductInfo } = await import('@/apis/mallApi');
      const allProductPromises = productIds.map(id => getProductInfo({ productId: id }));
      const allProducts = await Promise.all(allProductPromises);

      // 过滤掉已删除的商品（isActive: false）
      const activeProducts = allProducts.filter(product => product.isActive);

      // 设置总数（只计算激活的商品）
      setTotalProducts(activeProducts.length);

      if (activeProducts.length === 0) {
        setProducts([]);
        return;
      }

      // 计算分页（基于激活的商品）
      const startIndex = (page - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const paginatedProducts = activeProducts.slice(startIndex, endIndex);

      setProducts(paginatedProducts);

    } catch (error) {
      console.error('❌ [ProductManagement] 加载商品列表失败:', error);

      // 如果是合约未部署或其他错误，显示模拟数据
      if (error.message.includes('contract') ||
          error.message.includes('network') ||
          error.message.includes('ABI') ||
          error.message.includes('encoding')) {
        console.log('🔄 [ProductManagement] 使用模拟数据，原因:', error.message);
        const mockProducts = [
          {
            productId: 1,
            name: '示例商品1',
            description: '这是一个示例商品，用于展示商品管理功能',
            price: 100,
            stock: 50,
            isActive: true,
            sales: 5,
            images: []
          }
        ];
        setProducts(mockProducts);
        toast.success('🎉 趣拼团示例商品数据加载成功！愛拼才会赢！');
      } else {
        toast.error(`加载商品列表失败: ${error.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // 处理商品添加成功
  const handleProductAdded = (newProduct) => {
    setProducts(prev => [newProduct, ...prev]);
    setActiveTab('list'); // 切换回列表页面
  };

  // 处理编辑商品
  const handleEditProduct = (product) => {
    // 这里可以实现编辑功能，比如打开编辑模态框
    toast('编辑功能开发中，敬请期待！', {
      icon: 'ℹ️',
      duration: 3000,
    });
  };

  // 处理切换商品状态（上架/下架）
  const handleToggleProductStatus = async (product) => {
    if (!account) {
      toast.error('请先连接钱包');
      return;
    }

    const newStatus = !product.isActive;
    const actionText = newStatus ? '上架' : '下架';

    try {
      setIsLoading(true);

      // 创建signer对象
      const signer = { account: { address: account } };

      await toggleProductStatus({
        productId: product.productId,
        isActive: newStatus,
        signer
      });

      toast.success(`商品${actionText}成功！`);

      // 重新加载商品列表
      await loadProducts();

    } catch (error) {
      toast.error(`商品${actionText}失败: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理删除商品
  const handleDeleteProduct = async (product) => {
    if (!account) {
      toast.error('请先连接钱包');
      return;
    }

    // 确认删除
    if (!window.confirm(`确定要删除商品"${product.name}"吗？此操作不可恢复。`)) {
      return;
    }

    try {
      setIsLoading(true);

      // 创建signer对象
      const signer = { account: { address: account } };

      await deleteProduct({
        productId: product.productId,
        signer
      });

      toast.success('商品删除成功！');

      // 重新加载商品列表
      await loadProducts();

    } catch (error) {
      toast.error(`商品删除失败: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 查看商品图片
  const handleViewImages = (product) => {
    setSelectedProduct(product);
    setShowImageGallery(true);
  };

  // 分页控制函数
  const totalPages = Math.ceil(totalProducts / itemsPerPage);

  const handlePageChange = (page) => {
    setCurrentPage(page);
    loadProducts(page);
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadProducts();
  }, [account]);

  // 当页码变化时重新加载数据
  useEffect(() => {
    if (account) {
      loadProducts(currentPage);
    }
  }, [currentPage]);

  return (
    <div className="product-management">
      <div className="module-header">
        <h3>📦 商品管理</h3>
        <p>管理您的商品上架和库存</p>
      </div>

      {/* 功能导航 */}
      <div className="nav-wrapper">
        <div className="product-nav">
          <button
            className={`nav-btn ${activeTab === 'list' ? 'active' : ''}`}
            onClick={() => setActiveTab('list')}
          >
            📋 商品列表 ({totalProducts})
          </button>
          <button
            className={`nav-btn ${activeTab === 'add' ? 'active' : ''}`}
            onClick={() => setActiveTab('add')}
          >
            ➕ 添加商品
          </button>
          <button
            className={`nav-btn ${activeTab === 'orders' ? 'active' : ''}`}
            onClick={() => setActiveTab('orders')}
          >
            📊 订单管理
          </button>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="product-content">
        {activeTab === 'list' && (
          <div className="content-section">
            <div className="product-list-header">
              <h4>📋 商品列表</h4>
              <p>管理您的所有商品</p>
            </div>

            {isLoading ? (
              <div className="loading-container">
                <div className="loading-spinner">🔄</div>
                <p>正在加载商品列表...</p>
              </div>
            ) : products.length === 0 ? (
              <div className="empty-container">
                <div className="empty-icon">📦</div>
                <h3>暂无商品</h3>
                <p>您还没有添加任何商品</p>
                <button
                  className="add-first-btn"
                  onClick={() => setActiveTab('add')}
                >
                  ➕ 添加第一个商品
                </button>
              </div>
            ) : (
              <div className="product-grid">
                {products.map((product) => (
                  <div key={product.productId || product.id} className="product-card">
                    <div className="product-header">
                      <h5 className="product-name">{product.name}</h5>
                      <div className={`product-status ${product.isActive ? 'active' : 'inactive'}`}>
                        {product.isActive ? '✅ 上架中' : '⏸️ 已下架'}
                      </div>
                    </div>

                    {/* 商品图片 */}
                    {product.images && product.images.length > 0 && (
                      <div className="product-image" onClick={() => handleViewImages(product)}>
                        <img
                          src={product.images[0].startsWith('http')
                            ? product.images[0]
                            : `https://gateway.pinata.cloud/ipfs/${product.images[0]}`}
                          alt={product.name}
                          onError={(e) => {
                            // 尝试备用网关
                            if (e.target.src.includes('pinata')) {
                              e.target.src = `https://cloudflare-ipfs.com/ipfs/${product.images[0]}`;
                            } else if (e.target.src.includes('cloudflare')) {
                              e.target.src = `https://dweb.link/ipfs/${product.images[0]}`;
                            } else {
                              e.target.style.display = 'none';
                            }
                          }}
                        />
                        {product.images.length > 1 && (
                          <div className="image-count-badge">
                            📷 {product.images.length}
                          </div>
                        )}
                      </div>
                    )}

                    <div className="product-info">
                      <p className="product-description">
                        {product.description.length > 100
                          ? `${product.description.substring(0, 100)}...`
                          : product.description
                        }
                      </p>
                      <div className="product-details">
                        <div className="detail-item">
                          <span className="label">价格:</span>
                          <span className="value">
                            {formatPoints(product.price)} 积分
                          </span>
                        </div>
                        <div className="detail-item">
                          <span className="label">库存:</span>
                          <span className="value">{product.stock} 件</span>
                        </div>
                        <div className="detail-item">
                          <span className="label">销量:</span>
                          <span className="value">{product.sales || 0} 件</span>
                        </div>
                      </div>
                    </div>

                    <div className="product-actions">
                      <button
                        className="action-btn edit"
                        onClick={() => handleEditProduct(product)}
                        disabled={isLoading}
                      >
                        ✏️ 编辑
                      </button>
                      <button
                        className="action-btn toggle"
                        onClick={() => handleToggleProductStatus(product)}
                        disabled={isLoading}
                      >
                        {product.isActive ? '⏸️ 下架' : '▶️ 上架'}
                      </button>
                      <button
                        className="action-btn delete"
                        onClick={() => handleDeleteProduct(product)}
                        disabled={isLoading}
                      >
                        🗑️ 删除
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* 分页组件 */}
            {!isLoading && totalProducts > itemsPerPage && (
              <div className="pagination-container">
                <div className="pagination-info">
                  显示第 {((currentPage - 1) * itemsPerPage) + 1} - {Math.min(currentPage * itemsPerPage, totalProducts)} 项，共 {totalProducts} 项
                </div>
                <div className="pagination-controls">
                  <button
                    className="pagination-btn"
                    onClick={handlePrevPage}
                    disabled={currentPage === 1}
                  >
                    ← 上一页
                  </button>

                  {/* 页码按钮 */}
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                    <button
                      key={page}
                      className={`pagination-btn ${page === currentPage ? 'active' : ''}`}
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </button>
                  ))}

                  <button
                    className="pagination-btn"
                    onClick={handleNextPage}
                    disabled={currentPage === totalPages}
                  >
                    下一页 →
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'add' && (
          <div className="content-section">
            <AddProduct
              onProductAdded={async (result) => {
          
                toast.success('🎉 趣拼团商品添加成功！愛拼才会赢！');
                await loadProducts(); // 重新加载商品列表
                setActiveTab('list'); // 跳转到商品列表
              }}
            />
          </div>
        )}

        {activeTab === 'orders' && (
          <div className="content-section">
            <OrderManagement />
          </div>
        )}
      </div>

      {/* 功能说明 */}
      <div className="feature-notice">
        <h4>📖 功能说明</h4>
        <div className="notice-content">
          <p>商品管理功能将包含以下核心功能：</p>
          <div className="notice-grid">
            <div className="notice-item">
              <strong>商品上架</strong>
              <span>支持多图片上传，详细商品描述</span>
            </div>
            <div className="notice-item">
              <strong>库存管理</strong>
              <span>实时库存监控，自动补货提醒</span>
            </div>
            <div className="notice-item">
              <strong>订单处理</strong>
              <span>订单状态跟踪，发货物流管理</span>
            </div>
            <div className="notice-item">
              <strong>销售统计</strong>
              <span>销售数据分析，收益报表生成</span>
            </div>
          </div>
        </div>
      </div>

      {/* 图片画廊 */}
      {showImageGallery && selectedProduct && selectedProduct.images && selectedProduct.images.length > 0 && (
        <div className="image-gallery-overlay" onClick={() => setShowImageGallery(false)}>
          <div className="image-gallery-modal" onClick={(e) => e.stopPropagation()}>
            <div className="gallery-header">
              <h4>{selectedProduct.name} - 商品图片 ({selectedProduct.images.length})</h4>
              <button
                className="gallery-close-btn"
                onClick={() => setShowImageGallery(false)}
              >
                ✕
              </button>
            </div>
            <div className="gallery-content">
              <IPFSImageGrid
                hashes={selectedProduct.images}
                maxImages={selectedProduct.images.length}
                className="merchant-image-grid"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
